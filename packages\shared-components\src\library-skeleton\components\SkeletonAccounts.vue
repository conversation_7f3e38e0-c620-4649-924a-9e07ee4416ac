<template>
  <div class="skeleton-accounts-wrapper">
    <!-- 账号管理内容区域 -->
    <div class="skeleton-accounts-content">
      <!-- 蓝色渐变背景区域 -->
      <div class="skeleton-accounts-banner">
        <div class="skeleton-accounts-header">
          <div class="skeleton-title">账号管理</div>
          <div class="skeleton-add-btn">添加账号</div>
        </div>

        <!-- 已登录状态：三个账号框 -->
        <div v-if="isLoggedIn" class="skeleton-accounts-grid">
          <div class="skeleton-account-card skeleton-account-card--large">
          </div>
          <div class="skeleton-account-card skeleton-account-card--small">
          </div>
          <div class="skeleton-account-card skeleton-account-card--small">
          </div>
        </div>

        <!-- 未登录状态：添加账号区域 -->
        <div v-else class="skeleton-unlogged-content">
          <div class="skeleton-add-account-section">
            <div class="skeleton-add-account-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="72" height="72" viewBox="0 0 72 72" fill="none">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M72 51.4489L71.9998 51.6619C71.9992 51.9441 71.9974 52.2191 71.9944 52.4872L71.9891 52.8842C71.9829 53.276 71.974 53.6526 71.9625 54.015L71.9501 54.3726C71.8019 58.3139 71.3258 60.5188 70.5492 62.3634L70.4789 62.5277L70.4069 62.6902C70.3827 62.7441 70.3582 62.7978 70.3334 62.8513L70.2582 63.0111C70.2076 63.1172 70.1559 63.2227 70.1032 63.3278L70.0233 63.4851C70.0098 63.5113 69.9963 63.5374 69.9827 63.5636L69.9005 63.7204L69.8588 63.7988C68.4616 66.4113 66.4113 68.4616 63.7988 69.8588C63.7727 69.8727 63.7465 69.8866 63.7204 69.9005L63.5636 69.9827C63.5374 69.9963 63.5113 70.0098 63.4851 70.0233L63.3278 70.1032C63.2227 70.1559 63.1172 70.2076 63.0111 70.2582L62.8513 70.3334C62.7978 70.3582 62.7441 70.3827 62.6902 70.4069L62.5277 70.4789L62.3634 70.5492C60.5188 71.3258 58.3139 71.8019 54.3726 71.9501L54.015 71.9625C53.6526 71.974 53.276 71.9829 52.8842 71.9891L52.4872 71.9944C52.1521 71.9981 51.8062 72 51.4489 72H20.5511L20.3381 71.9998C20.0559 71.9992 19.7809 71.9974 19.5128 71.9944L19.1159 71.9891C18.724 71.9829 18.3474 71.974 17.985 71.9625L17.6274 71.9501C13.6861 71.8019 11.4812 71.3258 9.6366 70.5492L9.47234 70.4789L9.30978 70.4069C9.25586 70.3827 9.20218 70.3582 9.14871 70.3334L8.98891 70.2582C8.88275 70.2076 8.77729 70.1559 8.67223 70.1032L8.51491 70.0233C8.48873 70.0098 8.46257 69.9963 8.43641 69.9827L8.27959 69.9005L8.20122 69.8588C5.58872 68.4616 3.53842 66.4113 2.14124 63.7988C2.12726 63.7727 2.11336 63.7465 2.09952 63.7204L2.01726 63.5636C2.00368 63.5374 1.99016 63.5113 1.97671 63.4851L1.89682 63.3278C1.84408 63.2227 1.79239 63.1172 1.74177 63.0111L1.66662 62.8513C1.64184 62.7978 1.61732 62.7441 1.59307 62.6902L1.52112 62.5277L1.45077 62.3634C0.674169 60.5188 0.198101 58.3139 0.0499168 54.3726L0.0375238 54.015C0.0260088 53.6526 0.0171298 53.276 0.0109064 52.8842L0.00556905 52.4872C0.00186014 52.1521 0 51.8062 0 51.4489V51.6612L0.000223126 20.3381C0.000520566 20.197 0.00111526 20.0577 0.00200649 19.9202L0.00556905 19.5128L0.0109064 19.1159C0.0171298 18.724 0.0260088 18.3474 0.0375238 17.985L0.0499168 17.6274C0.198101 13.6861 0.674169 11.4812 1.45077 9.6366L1.52112 9.47234L1.59307 9.30978C1.61732 9.25586 1.64184 9.20218 1.66662 9.14871L1.74177 8.98891C1.79239 8.88275 1.84408 8.77729 1.89682 8.67223L1.97671 8.51491C1.99016 8.48873 2.00368 8.46257 2.01726 8.43641L2.09952 8.27959L2.14124 8.20122C3.53842 5.58872 5.58872 3.53842 8.20122 2.14124C8.22735 2.12726 8.25347 2.11336 8.27959 2.09952L8.43641 2.01726C8.46257 2.00368 8.48873 1.99016 8.51491 1.97671L8.67223 1.89682C8.77729 1.84408 8.88275 1.79239 8.98891 1.74177L9.14871 1.66662C9.20218 1.64184 9.25586 1.61732 9.30978 1.59307L9.47234 1.52112L9.6366 1.45077C11.4812 0.674169 13.6861 0.198101 17.6274 0.0499168L17.985 0.0375238C18.3474 0.0260088 18.724 0.0171298 19.1159 0.0109064L19.5128 0.00556905C19.8479 0.00186014 20.1938 0 20.5511 0L51.6619 0.000223126C51.9441 0.000818006 52.2191 0.00260192 52.4872 0.00556905L52.8842 0.0109064C53.276 0.0171298 53.6526 0.0260088 54.015 0.0375238L54.3726 0.0499168C58.3139 0.198101 60.5188 0.674169 62.3634 1.45077L62.5277 1.52112L62.6902 1.59307C62.7441 1.61732 62.7978 1.64184 62.8513 1.66662L63.0111 1.74177C63.1172 1.79239 63.2227 1.84408 63.3278 1.89682L63.4851 1.97671C63.5113 1.99016 63.5374 2.00368 63.5636 2.01726L63.7204 2.09952L63.7988 2.14124C66.4113 3.53842 68.4616 5.58872 69.8588 8.20122C69.8727 8.22735 69.8866 8.25347 69.9005 8.27959L69.9827 8.43641C69.9963 8.46257 70.0098 8.48873 70.0233 8.51491L70.1032 8.67223C70.1559 8.77729 70.2076 8.88275 70.2582 8.98891L70.3334 9.14871C70.3582 9.20218 70.3827 9.25586 70.4069 9.30978L70.4789 9.47234L70.5492 9.6366C71.3258 11.4812 71.8019 13.6861 71.9501 17.6274L71.9625 17.985C71.974 18.3474 71.9829 18.724 71.9891 19.1159L71.9944 19.5128C71.9981 19.8479 72 20.1938 72 20.5511V51.4489Z" fill="#F1F2F3"/>
                <path d="M22.9573 20.8827C23.5197 20.5594 24.2229 20.9673 24.2229 21.6141V41.5614C24.2229 42.0606 24.4901 42.5251 24.926 42.7782L31.0012 46.287C31.4722 46.5537 32.0559 46.2159 32.0559 45.6747V40.7811C32.0559 40.3171 32.4356 39.9374 32.8997 39.9374H39.0725C39.5366 39.9374 39.9162 40.3171 39.9163 40.7811V59.5194C39.9163 60.1663 39.2131 60.5743 38.6506 60.2508L32.0559 56.4471L24.2229 51.9256L17.093 47.8124C16.6571 47.5592 16.3899 47.0948 16.3899 46.5956V25.4813C16.3899 24.9821 16.6571 24.5176 17.093 24.2645L17.1213 24.2508L22.9573 20.8827ZM32.0833 12.4735C32.0833 11.8267 32.7864 11.4189 33.3489 11.742L39.9377 15.5526L47.7708 20.0741L54.9075 24.1874C55.3433 24.4405 55.6106 24.905 55.6106 25.4042V46.5184C55.6106 47.0175 55.3433 47.4821 54.9075 47.7352L54.8792 47.7489L49.0432 51.117C48.4807 51.4405 47.7776 51.0325 47.7776 50.3856V30.4383C47.7776 29.9391 47.5104 29.4747 47.0745 29.2215L40.9993 25.7127C40.5282 25.4458 39.9446 25.7837 39.9446 26.3251V31.2186C39.9446 31.6827 39.5649 32.0624 39.1008 32.0624H32.927C32.4631 32.0621 32.0833 31.6825 32.0833 31.2186V12.4735Z" fill="#393C3F"/>
                <path d="M54.8158 24.183C55.2452 24.4296 55.5949 25.0373 55.5949 25.5339V46.4248C55.5949 46.9214 55.2452 47.5291 54.8158 47.7756L48.5312 51.4114C48.1018 51.6579 47.7521 51.4565 47.7521 50.96V30.5205C47.7521 30.0239 47.4024 29.4162 46.973 29.1696L40.6918 25.5304C40.2624 25.2838 39.9127 25.4853 39.9127 25.9818V31.1525C39.9127 31.6491 39.5111 32.0519 39.0124 32.0519H32.9702C32.475 32.0519 32.0734 31.6491 32.0734 31.1525V11.9492C32.0734 11.4526 32.4231 11.2512 32.8524 11.4978L35.1932 12.8521C35.6225 13.0986 35.9757 13.3 35.9827 13.2966C35.9896 13.2931 36.3428 13.4945 36.7721 13.7411L54.8158 24.183ZM32.0768 40.8131C32.0768 40.3166 32.4785 39.9137 32.9736 39.9137H39.0159C39.5111 39.9137 39.9127 40.3166 39.9162 40.8131V60.0511C39.9162 60.5477 39.5665 60.7491 39.1371 60.5026L31.4986 56.0785C31.0692 55.832 30.7229 55.6271 30.7229 55.6236C30.7229 55.6201 30.3732 55.4153 29.9439 55.1687L17.1841 47.7791C16.7548 47.5325 16.405 46.9248 16.405 46.4283V25.5373C16.405 25.0408 16.7548 24.4331 17.1841 24.1865L23.4688 20.5507C23.8981 20.3042 24.2479 20.5056 24.2479 21.0022V41.4451C24.2479 41.9417 24.5976 42.5494 25.0235 42.796L31.3081 46.4317C31.7375 46.6783 32.0872 46.4769 32.0872 45.9803L32.0768 40.8131Z" fill="#393C3F"/>
              </svg>
            </div>
            <div class="skeleton-add-account-text">
              <div class="skeleton-add-account-title">添加账号</div>
              <div class="skeleton-add-account-subtitle">解锁更多精彩内容</div>
            </div>
          </div>

          <div class="skeleton-placeholder-container">
            <div class="skeleton-placeholder-first-box">
              <div class="skeleton-placeholder-hexagon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 2L21.196 7V17L12 22L2.804 17V7L12 2Z" fill="#FF4444" stroke="#FF4444" stroke-width="2"/>
                </svg>
              </div>
              <div class="skeleton-placeholder-first-content">
                <div class="skeleton-placeholder-text">国家：*</div>
                <div class="skeleton-placeholder-text">年限：*</div>
              </div>
            </div>

            <div class="skeleton-placeholder-second-box">
              <div class="skeleton-placeholder-item">
                <div class="skeleton-placeholder-icon">***</div>
                <div class="skeleton-placeholder-text">游戏价值￥</div>
              </div>
              <div class="skeleton-placeholder-item">
                <div class="skeleton-placeholder-icon">***</div>
                <div class="skeleton-placeholder-text">游戏时长 h</div>
              </div>
              <div class="skeleton-placeholder-item">
                <div class="skeleton-placeholder-icon">***</div>
                <div class="skeleton-placeholder-text">游戏数量</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="SkeletonAccounts">
// 账号管理区域骨架屏

// 定义 props
const props = defineProps({
  isLoggedIn: {
    type: Boolean,
    default: true
  }
});
</script>

<style lang="scss" scoped>
.skeleton-accounts-wrapper {
  margin-bottom: 24px;

  .skeleton-accounts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 21px;
    height: 52px;

    .skeleton-title {
      color: var(--white-gamerecord-color-white-100a, #FFF);

      font-family: "Microsoft YaHei";
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      line-height: 22px;
      letter-spacing: 0.16px;
    }

    .skeleton-add-btn {
      display: inline-flex;
      height: 30px;
      flex-direction: column;
      align-items: center;
      flex-shrink: 0;
      padding: 6px 10px;
      border-radius: var(---26x32, 4px);
      background: var(--white-gamerecord-color-white-20a, rgba(255, 255, 255, 0.20));
      color: var(--white-gamerecord-color-white-100a, #FFF);
      text-align: center;
      font-family: "Microsoft YaHei";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
      letter-spacing: 0.12px;
    }
  }

  .skeleton-accounts-content {
    .skeleton-accounts-banner {
      width: 100%;
      height: 270px;
      background: linear-gradient(90deg, #253F78 0%, #1B73A5 100%);
      border-radius: 8px;
      position: relative;
      overflow: hidden;

      .skeleton-accounts-header {
        padding: 20px 24px 16px 24px;
        position: relative;
        z-index: 1;
        border-bottom: 1px solid var(--white-gamerecord-color-white-05a, rgba(255, 255, 255, 0.05));
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
          rgba(255, 255, 255, 0.1) 0%,
          rgba(255, 255, 255, 0.05) 50%,
          rgba(0, 0, 0, 0.1) 100%
        );
        z-index: 1;
      }

      .skeleton-accounts-grid {
        display: flex;
        gap: 16px;
        padding: 0 24px 24px 24px;
        position: relative;
        z-index: 1;

        .skeleton-account-card {
          height: 170px;
          background: var(--white-gamerecord-color-white-05a,rgba(255, 255, 255, 0.05));
          border: 1px solid var(--white-gamerecord-color-white-05a,rgba(255, 255, 255, 0.05));
          border-radius: 8px;
          backdrop-filter: blur(10px);
          transition: all 0.2s ease;

          &--large {
            width: 508px;
          }

          &--small {
            width: 158px;
          }
        }
      }
    }

    .skeleton-unlogged-content {
      margin-right: 24px;
      margin-left: 24px;
      height: 170px;
      flex: 1 0 0;
      border-radius: 8px;
      border: 1px solid var(--white-gamerecord-color-white-05a, rgba(255, 255, 255, 0.05));
      background: var(--white-gamerecord-color-white-05a, rgba(255, 255, 255, 0.05));
      // padding: 14px 16px 16px 16px;
      position: relative;

      .skeleton-add-account-section {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-top: 14px;
        margin-left: 16px;
        margin-bottom: 12px;

        .skeleton-add-account-icon {
          flex-shrink: 0;
        }

        .skeleton-add-account-text {
          .skeleton-add-account-title {
            color: #FFF;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
          }

          .skeleton-add-account-subtitle {
            color: var(--white-gamerecord-color-white-70a, rgba(255, 255, 255, 0.70));
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 16px;
          }
        }
      }

      .skeleton-placeholder-container {
        display: flex;
        gap: 10px;
        padding: 0 16px 14px 16px;
      }

      .skeleton-placeholder-first-box {
        display: flex;
        width: 116px;
        height: 58px;
        padding: 12px 10px;
        align-items: center;
        flex-shrink: 0;
        border-radius: 5px;
        border: 1px solid var(--white-gamerecord-color-white-05a, rgba(255, 255, 255, 0.05));
        background: var(--white-gamerecord-color-white-05a, rgba(255, 255, 255, 0.05));
        gap: 12px;

        .skeleton-placeholder-hexagon {
          flex-shrink: 0;
        }

        .skeleton-placeholder-first-content {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .skeleton-placeholder-text {
            color: var(--white-gamerecord-color-white-60a, rgba(255, 255, 255, 0.6));
            font-family: "Microsoft YaHei";
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
          }
        }
      }

      .skeleton-placeholder-second-box {
        display: flex;
        flex: 1;
        height: 58px;
        padding: 14px 10px;
        align-items: center;
        flex-shrink: 0;
        border-radius: $general-size-radius-5;
        border: 1px solid var(--white-gamerecord-color-white-05a, rgba(255, 255, 255, 0.05));
        background: var(--white-gamerecord-color-white-05a, rgba(255, 255, 255, 0.05));

        .skeleton-placeholder-item {
          flex: 1;
          text-align: center;

          .skeleton-placeholder-icon {
            color: var(--white-gamerecord-color-white-40a, rgba(255, 255, 255, 0.4));
            font-family: "Microsoft YaHei";
            font-size: 24px;
            font-weight: 600;
            line-height: 32px;
            margin-bottom: 8px;
          }

          .skeleton-placeholder-text {
            color: var(--white-gamerecord-color-white-60a, rgba(255, 255, 255, 0.6));
            font-family: "Microsoft YaHei";
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
          }
        }
      }
    }
  }
}
</style>
