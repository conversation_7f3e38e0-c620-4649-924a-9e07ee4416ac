<template>
  <div class="skeleton-accounts-wrapper">
    <!-- 账号管理内容区域 -->
    <div class="skeleton-accounts-content">
      <!-- 蓝色渐变背景区域 -->
      <div class="skeleton-accounts-banner">
        <div class="skeleton-accounts-header">
          <div class="skeleton-title">账号管理</div>
          <div class="skeleton-add-btn" v-if="isLoggedIn">添加账号</div>
        </div>

        <!-- 已登录状态：三个账号框 -->
        <div v-if="isLoggedIn" class="skeleton-accounts-grid">
          <div class="skeleton-account-card skeleton-account-card--large">
          </div>
          <div class="skeleton-account-card skeleton-account-card--small">
          </div>
          <div class="skeleton-account-card skeleton-account-card--small">
          </div>
        </div>

        <!-- 未登录状态：添加账号区域 -->
        <div v-else class="skeleton-unlogged-content">
          <div class="skeleton-add-account-section">
            <div class="skeleton-add-account-icon">
              <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="48" height="48" rx="8" fill="rgba(255, 255, 255, 0.1)"/>
                <path d="M24 16v16m-8-8h16" stroke="white" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </div>
            <div class="skeleton-add-account-text">
              <div class="skeleton-add-account-title">添加账号</div>
              <div class="skeleton-add-account-subtitle">登录更多好友的网络</div>
            </div>
          </div>

          <!-- 三个占位符 -->
          <div class="skeleton-placeholder-grid">
            <div class="skeleton-placeholder-item">
              <div class="skeleton-placeholder-icon">***</div>
              <div class="skeleton-placeholder-text">游戏好友1</div>
            </div>
            <div class="skeleton-placeholder-item">
              <div class="skeleton-placeholder-icon">***</div>
              <div class="skeleton-placeholder-text">游戏好友2</div>
            </div>
            <div class="skeleton-placeholder-item">
              <div class="skeleton-placeholder-icon">***</div>
              <div class="skeleton-placeholder-text">游戏好友3</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="SkeletonAccounts">
// 账号管理区域骨架屏

// 定义 props
const props = defineProps({
  isLoggedIn: {
    type: Boolean,
    default: true
  }
});
</script>

<style lang="scss" scoped>
.skeleton-accounts-wrapper {
  margin-bottom: 24px;

  .skeleton-accounts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 21px;
    height: 52px;

    .skeleton-title {
      color: var(--white-gamerecord-color-white-100a, #FFF);

      font-family: "Microsoft YaHei";
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      line-height: 22px;
      letter-spacing: 0.16px;
    }

    .skeleton-add-btn {
      display: inline-flex;
      height: 30px;
      flex-direction: column;
      align-items: center;
      flex-shrink: 0;
      padding: 6px 10px;
      border-radius: var(---26x32, 4px);
      background: var(--white-gamerecord-color-white-20a, rgba(255, 255, 255, 0.20));
      color: var(--white-gamerecord-color-white-100a, #FFF);
      text-align: center;
      font-family: "Microsoft YaHei";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
      letter-spacing: 0.12px;
    }
  }

  .skeleton-accounts-content {
    .skeleton-accounts-banner {
      width: 100%;
      height: 270px;
      background: linear-gradient(90deg, #253F78 0%, #1B73A5 100%);
      border-radius: 8px;
      position: relative;
      overflow: hidden;

      .skeleton-accounts-header {
        padding: 20px 24px 16px 24px;
        position: relative;
        z-index: 1;
        border-bottom: 1px solid var(--white-gamerecord-color-white-05a, rgba(255, 255, 255, 0.05));
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
          rgba(255, 255, 255, 0.1) 0%,
          rgba(255, 255, 255, 0.05) 50%,
          rgba(0, 0, 0, 0.1) 100%
        );
        z-index: 1;
      }

      .skeleton-accounts-grid {
        display: flex;
        gap: 16px;
        padding: 0 24px 24px 24px;
        position: relative;
        z-index: 1;

        .skeleton-account-card {
          height: 170px;
          background: var(--white-gamerecord-color-white-05a,rgba(255, 255, 255, 0.05));
          border: 1px solid var(--white-gamerecord-color-white-05a,rgba(255, 255, 255, 0.05));
          border-radius: 8px;
          backdrop-filter: blur(10px);
          transition: all 0.2s ease;

          &--large {
            width: 508px;
          }

          &--small {
            width: 158px;
          }
        }
      }

      // 未登录状态样式
      .skeleton-unlogged-content {
        padding: 0 24px 24px 24px;
        position: relative;
        z-index: 1;

        .skeleton-add-account-section {
          display: flex;
          align-items: center;
          gap: 16px;
          margin-bottom: 32px;

          .skeleton-add-account-icon {
            flex-shrink: 0;
          }

          .skeleton-add-account-text {
            .skeleton-add-account-title {
              color: var(--white-gamerecord-color-white-100a, #FFF);
              font-family: "Microsoft YaHei";
              font-size: 16px;
              font-weight: 600;
              line-height: 22px;
              margin-bottom: 4px;
            }

            .skeleton-add-account-subtitle {
              color: var(--white-gamerecord-color-white-60a, rgba(255, 255, 255, 0.6));
              font-family: "Microsoft YaHei";
              font-size: 12px;
              font-weight: 400;
              line-height: 16px;
            }
          }
        }

        .skeleton-placeholder-grid {
          display: flex;
          gap: 24px;
          justify-content: space-between;

          .skeleton-placeholder-item {
            flex: 1;
            text-align: center;

            .skeleton-placeholder-icon {
              color: var(--white-gamerecord-color-white-40a, rgba(255, 255, 255, 0.4));
              font-family: "Microsoft YaHei";
              font-size: 24px;
              font-weight: 600;
              line-height: 32px;
              margin-bottom: 8px;
            }

            .skeleton-placeholder-text {
              color: var(--white-gamerecord-color-white-60a, rgba(255, 255, 255, 0.6));
              font-family: "Microsoft YaHei";
              font-size: 12px;
              font-weight: 400;
              line-height: 16px;
            }
          }
        }
      }
    }
  }
}
</style>
