<template>
  <div class="skeleton-games-unlogged-wrapper">
    <!-- 管理游戏请先安装Steam 提示区域 -->
    <div class="skeleton-steam-notice">
      <div class="skeleton-steam-icon">
        <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
          <!-- Steam 手柄图标 -->
          <rect width="64" height="64" rx="12" fill="#8C9196" opacity="0.1"/>
          <path d="M32 20c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm-8 12c0-4.418 3.582-8 8-8s8 3.582 8 8-3.582 8-8 8-8-3.582-8-8z" fill="#8C9196"/>
          <circle cx="28" cy="28" r="2" fill="#8C9196"/>
          <circle cx="36" cy="28" r="2" fill="#8C9196"/>
          <circle cx="28" cy="36" r="2" fill="#8C9196"/>
          <circle cx="36" cy="36" r="2" fill="#8C9196"/>
        </svg>
      </div>
      <div class="skeleton-steam-text">管理游戏请先安装Steam</div>
      <div class="skeleton-steam-button">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M8 0C3.58 0 0 3.58 0 8s3.58 8 8 8 8-3.58 8-8-3.58-8-8-8zm3.5 9H9v2.5c0 .28-.22.5-.5.5s-.5-.22-.5-.5V9H6.5c-.28 0-.5-.22-.5-.5s.22-.5.5-.5H8V6.5c0-.28.22-.5.5-.5s.5.22.5.5V8h1.5c.28 0 .5.22.5.5s-.22.5-.5.5z" fill="white"/>
        </svg>
        前往官网
      </div>
    </div>
  </div>
</template>

<script setup name="SkeletonGamesUnlogged">
// 未登录状态的游戏区域骨架屏组件
</script>

<style lang="scss" scoped>
.skeleton-games-unlogged-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 0 24px;

  .skeleton-steam-notice {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    max-width: 320px;

    .skeleton-steam-icon {
      margin-bottom: 24px;
      opacity: 0.8;
    }

    .skeleton-steam-text {
      color: #111111;
      font-family: "Microsoft YaHei";
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      margin-bottom: 24px;
    }

    .skeleton-steam-button {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      background: #4A90E2;
      color: white;
      border-radius: 6px;
      font-family: "Microsoft YaHei";
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #357ABD;
        transform: translateY(-1px);
      }

      svg {
        flex-shrink: 0;
      }
    }
  }
}
</style>
