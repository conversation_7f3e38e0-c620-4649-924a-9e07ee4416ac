<template>
  <div class="layout-main">
    <!-- <template v-if="isInitSteamData">
      <div class="steam-data-loading-wrapper">
        <div class="steam-data-loading-container">
          <div class="link-images">
            <img src="https://imgheybox.max-c.com/oa/2025/07/15/********************************.png">
            <i class="iconfont icon-pc-link-filled"></i>
            <img src="https://imgheybox.max-c.com/oa/2025/07/15/********************************.png">
          </div>
          <div class="progress">
            <div class="progress-bar" :style="{ width: `${progress}%` }">
              <div class="progress-bar-inner"></div>
            </div>
          </div>
          <div class="progress-desc">
            资源加载中 {{ progress }}%
          </div>
        </div>
      </div>
    </template>
    <template v-else> -->
      <div class="left-side" :style="{ width: leftSideWidth + 'px' }">
        <GameSideBar :isSteamInstalled="isSteamInstalled" />
      </div>
      <div class="resizer" @mousedown="handleMouseDown"></div>
      <main class="main-content">
        <router-view :isSteamInstalled="isSteamInstalled"></router-view>
      </main>
    <!-- </template> -->
  </div>
</template>

<script setup>
import GameSideBar from './components/GameSideBar/index.vue';
import { onMounted, onBeforeUnmount, ref, computed, watch } from 'vue';
import SteamManager from '_js/steam-manager';
import { useStore } from 'vuex';

const store = useStore();

const isInitSteamData = computed(() => store.state.is_init_steam_data);

// Steam 安装状态
const isSteamInstalled = ref(true); // 默认为 true，避免闪烁

const progress = ref(0);

let progressInterval = null;

const leftSideWidth = ref(280);

// 检测 Steam 是否安装
const checkSteamInstallation = async () => {
  try {
    const steamResult = await window.electronAPI.checkSteamInstallation();
    console.log('[Library] Steam 检测结果:', steamResult);
    isSteamInstalled.value = steamResult.installed;
  } catch (error) {
    console.error('[Library] Steam 检测失败:', error);
    isSteamInstalled.value = false;
  }
};

const handleMouseMove = ref(null);
const handleMouseUp = ref(null);

onMounted(async () => {
  startProgressUpdate();
  watch(progress, (newVal) => {
    if (newVal >= 100 && progressInterval) {
      stopProgressUpdate();
    }
  });

  await SteamManager.init()
  // 启动进度更新
})

onBeforeUnmount(() => {
  SteamManager.destroy();
  document.removeEventListener('mousemove', handleMouseMove.value);
  document.removeEventListener('mouseup', handleMouseUp.value);
  stopProgressUpdate();
})

// 开始进度更新
const startProgressUpdate = () => {
  progressInterval = setInterval(() => {
    if (progress.value < 100) {
      progress.value += Math.random() * 2 + 1;
    }

    if (progress.value > 100) {
      progress.value = 100;
    } else {
      progress.value = Math.floor(progress.value);
    }
  }, 33);
};

// 停止进度更新
const stopProgressUpdate = () => {
  if (progressInterval) {
    clearInterval(progressInterval);
    progressInterval = null;
  }
};

// 完成进度到100%
const completeProgress = () => {
  stopProgressUpdate();
  progress.value = 100;
};

// 监听isInitSteamData变化
const watchInitSteamData = () => {
  if (!isInitSteamData.value) {
    completeProgress();
  }
};

const handleMouseDown = (e) => {
  e.preventDefault();
  const startX = e.clientX;
  const startWidth = leftSideWidth.value;

  handleMouseMove.value = (moveEvent) => {
    const dx = moveEvent.clientX - startX;
    let newWidth = startWidth + dx;
    if (newWidth < 240) {
      newWidth = 240;
    }
    if (newWidth > 360) {
      newWidth = 360;
    }
    leftSideWidth.value = newWidth;
  };

  handleMouseUp.value = () => {
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
    document.removeEventListener('mousemove', handleMouseMove.value);
    document.removeEventListener('mouseup', handleMouseUp.value);
    e.target.classList.remove('active');
  };

  document.body.style.cursor = 'col-resize';
  document.body.style.userSelect = 'none';
  e.target.classList.add('active');

  document.addEventListener('mousemove', handleMouseMove.value);
  document.addEventListener('mouseup', handleMouseUp.value);
};

// 监听isInitSteamData变化
watch(isInitSteamData, (newVal) => {
  if (!newVal) {
    completeProgress();
  }
});

onMounted(() => {
  checkSteamInstallation();
});
</script>
<style lang="scss">
.layout-main {
  display: flex;
  width: 100%;
  height: 100vh;
  border-radius: 8px;
  background-color: #FFF;
  & > .left-side {
    border-radius: 8px 0px 0px 8px;
    overflow: hidden;
    height: 100vh;
    border: 1px solid var(---balck-gamerecord-color-black-05a, rgba(0, 0, 0, 0.05));
    border-right: none;
    flex-shrink: 0;
  }
  .resizer {
    width: 2px;
    cursor: col-resize;
    flex-shrink: 0;
    background-color: var(---balck-gamerecord-color-black-05a, rgba(0, 0, 0, 0.05));
  }
  .main-content {
    border-radius: 0px 8px 8px 0px;
    overflow: hidden;
    flex: 1;
    // 右侧滚动条
    width: calc(100% + 8px);
    margin-right: -8px;
  }
  .steam-data-loading-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .steam-data-loading-container {
      width: 360px;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    .link-images {
      width: 100%;
      margin-bottom: 142px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      img {
        width: 56px;
        height: 56px;
      }
      .iconfont {
        font-size: 24px;
        color: $general-color-text-1;
      }
    }
    .progress {
      width: 100%;
      height: 8px;
      border-radius: 4px;
      background: $general-color-stroke-0;
      margin-bottom: 10px;
      .progress-bar {
        position: relative;
        width: 100%;
        height: 100%;
        border-radius: 4px;
        overflow: hidden;
        .progress-bar-inner {
          position: absolute;
          width: 800px;
          height: 100%;
          left: 0;
          background: linear-gradient(90deg, #14191E 0%, #64696E 17%, #8C9196 25%, #64696E 37%, #14191E 50%, #64696E 67%, #8C9196 75%, #64696E 87%, #14191E 100%);
          animation: progressMove 3s linear infinite;
        }
      }
    }
    .progress-desc {
      color: $general-color-text-3;
      font-size: 14px;
    }
  }

  // 进度条动画关键帧
  @keyframes progressMove {
    0% {
      left: 0px;
    }
    100% {
      left: -400px;
    }
  }
}
</style>
